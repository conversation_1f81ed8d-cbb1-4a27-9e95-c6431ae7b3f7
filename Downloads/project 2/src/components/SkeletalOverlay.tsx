import React, { useState, useEffect } from 'react';
import { ActivityType } from '../types';

interface PoseDataPoint {
  frame_number: number;
  timestamp_seconds: number;
  pose_detected: boolean;
  detection_confidence: number;
  hip_angle: number;
  knee_angle: number;
  ankle_angle: number;
  trunk_angle: number;
  neck_angle: number;
  hip_x: number;
  hip_y: number;
  knee_x: number;
  knee_y: number;
  ankle_x: number;
  ankle_y: number;
  trunk_x: number;
  trunk_y: number;
  neck_x: number;
  neck_y: number;

  // Enhanced anatomical keypoints from BlazePose (match database schema)
  shoulder_x?: number;
  shoulder_y?: number;
  elbow_x?: number;
  elbow_y?: number;
  wrist_x?: number;
  wrist_y?: number;
  heel_x?: number;
  heel_y?: number;
  foot_x?: number;
  foot_y?: number;

  // Bilateral keypoints for comprehensive analysis
  shoulder_left_x?: number;
  shoulder_left_y?: number;
  shoulder_right_x?: number;
  shoulder_right_y?: number;
  elbow_left_x?: number;
  elbow_left_y?: number;
  elbow_right_x?: number;
  elbow_right_y?: number;
  wrist_left_x?: number;
  wrist_left_y?: number;
  wrist_right_x?: number;
  wrist_right_y?: number;
  hip_left_x?: number;
  hip_left_y?: number;
  hip_right_x?: number;
  hip_right_y?: number;
  knee_left_x?: number;
  knee_left_y?: number;
  knee_right_x?: number;
  knee_right_y?: number;
  ankle_left_x?: number;
  ankle_left_y?: number;
  ankle_right_x?: number;
  ankle_right_y?: number;

  // Enhanced confidence scores
  detection_quality?: number;
  bilateral_symmetry?: number;

  // Calculated metrics for this frame
  stride_length?: number;
  foot_strike_type?: string;
  posture_score?: number;

  // Raw keypoint data (JSON for flexibility)
  raw_keypoints?: any;
}

interface SkeletalOverlayProps {
  activity: ActivityType;
  view: 'side' | 'rear';
  currentTime: number;
  duration: number;
  videoRef: React.RefObject<HTMLVideoElement>;
  sessionId?: string;
}

interface BiomechanicalJoint {
  x: number;
  y: number;
  confidence: number;
  anatomicallyAdjusted: boolean;
  detected: boolean;
}

interface ProfessionalSkeleton {
  // Central axis
  head: BiomechanicalJoint;
  neck: BiomechanicalJoint;
  c7: BiomechanicalJoint;  // 7th cervical vertebra
  t12: BiomechanicalJoint; // 12th thoracic vertebra
  l5: BiomechanicalJoint;  // 5th lumbar vertebra
  sacrum: BiomechanicalJoint;

  // Primary leg (detected side)
  hipCenter: BiomechanicalJoint;
  hip: BiomechanicalJoint;
  knee: BiomechanicalJoint;
  ankle: BiomechanicalJoint;
  heel: BiomechanicalJoint;
  forefoot: BiomechanicalJoint;
  toes: BiomechanicalJoint;

  // Secondary leg (mirrored)
  hipMirrored: BiomechanicalJoint;
  kneeMirrored: BiomechanicalJoint;
  ankleMirrored: BiomechanicalJoint;
  heelMirrored: BiomechanicalJoint;
  forefootMirrored: BiomechanicalJoint;
  toesMirrored: BiomechanicalJoint;

  // Primary arm (detected side)
  shoulder: BiomechanicalJoint;
  elbow: BiomechanicalJoint;
  wrist: BiomechanicalJoint;
  hand: BiomechanicalJoint;

  // Secondary arm (mirrored)
  shoulderMirrored: BiomechanicalJoint;
  elbowMirrored: BiomechanicalJoint;
  wristMirrored: BiomechanicalJoint;
  handMirrored: BiomechanicalJoint;
}

interface AnatomicalAngle {
  name: string;
  value: number;
  vertex: BiomechanicalJoint;
  point1: BiomechanicalJoint;
  point2: BiomechanicalJoint;
  significance: 'primary' | 'secondary' | 'tertiary';
  confidence: number;
}

const SkeletalOverlay: React.FC<SkeletalOverlayProps> = ({
  activity,
  view,
  currentTime,
  videoRef,
  sessionId
}) => {
  const [poseData, setPoseData] = useState<PoseDataPoint[]>([]);
  const [currentPose, setCurrentPose] = useState<PoseDataPoint | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // BIOMECHANICAL CONSTANTS for anatomical accuracy
  const ANATOMICAL_RATIOS = {
    headToNeck: 0.08,
    neckToShoulder: 0.12,
    shoulderToElbow: 0.18,
    elbowToWrist: 0.15,
    wristToHand: 0.08,
    spineC7ToT12: 0.15,
    spineT12ToL5: 0.10,
    spineL5ToSacrum: 0.05,
    hipToKnee: 0.25,
    kneeToAnkle: 0.23,
    ankleToHeel: 0.05,
    heelToForefoot: 0.12,
    forefootToToes: 0.08,
    shoulderWidth: 0.22,
    hipWidth: 0.18
  };

  // Load pose data with optimized binary search
  useEffect(() => {
    if (sessionId) {
      loadPoseData();
    }
  }, [sessionId]);

  useEffect(() => {
    if (poseData.length > 0) {
      const closestPose = findClosestPoseOptimized(poseData, currentTime);
      if (!currentPose || closestPose.frame_number !== currentPose.frame_number) {
        setCurrentPose(closestPose);
      }
    }
  }, [currentTime, poseData, currentPose]);

  const findClosestPoseOptimized = (data: PoseDataPoint[], targetTime: number): PoseDataPoint => {
    if (data.length === 0) return data[0];
    if (data.length === 1) return data[0];

    let left = 0;
    let right = data.length - 1;
    let closest = data[0];
    let minDiff = Math.abs(data[0].timestamp_seconds - targetTime);

    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const diff = Math.abs(data[mid].timestamp_seconds - targetTime);

      if (diff < minDiff) {
        minDiff = diff;
        closest = data[mid];
      }

      if (data[mid].timestamp_seconds < targetTime) {
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }

    return closest;
  };

  const loadPoseData = async () => {
    if (!sessionId) return;

    setIsLoading(true);
    try {
      const { default: supabase } = await import('../utils/supabaseClient');

      const { data, error } = await supabase
        .from('pose_data')
        .select('*')
        .eq('session_id', sessionId)
        .order('frame_number');

      if (error) {
        setError(`Failed to load pose data: ${error.message}`);
        return;
      }

      if (!data || data.length === 0) {
        setError('No pose data found for this session');
        setPoseData([]);
      } else {
        setPoseData(data);
        setError(null);
      }
    } catch (err) {
      setError(`Failed to load pose data: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // BIOMECHANICAL JOINT POSITIONING with anatomical constraints
  const applyAnatomicalConstraints = (rawPose: PoseDataPoint): ProfessionalSkeleton => {
    const baseConfidence = rawPose.detection_confidence || 0.8;

    // Debug: Log detected vs estimated keypoints usage
    const detectedKeypoints = {
      shoulder: !!(rawPose.shoulder_x !== undefined && rawPose.shoulder_x !== null),
      elbow: !!(rawPose.elbow_x !== undefined && rawPose.elbow_x !== null),
      wrist: !!(rawPose.wrist_x !== undefined && rawPose.wrist_x !== null),
      heel: !!(rawPose.heel_x !== undefined && rawPose.heel_x !== null),
      foot: !!(rawPose.foot_x !== undefined && rawPose.foot_x !== null)
    };

    if (rawPose.frame_number % 30 === 0) { // Log every 30th frame to avoid spam
      console.log(`Frame ${rawPose.frame_number} - Detected keypoints:`, detectedKeypoints);
      console.log(`Frame ${rawPose.frame_number} - Enhanced keypoint values:`, {
        shoulder: { x: rawPose.shoulder_x, y: rawPose.shoulder_y },
        elbow: { x: rawPose.elbow_x, y: rawPose.elbow_y },
        wrist: { x: rawPose.wrist_x, y: rawPose.wrist_y },
        heel: { x: rawPose.heel_x, y: rawPose.heel_y },
        foot: { x: rawPose.foot_x, y: rawPose.foot_y }
      });
    }

    // Calculate body scale from detected keypoints
    const estimatedBodyHeight = Math.abs((rawPose.neck_y || 10) - (rawPose.ankle_y || 90));
    const bodyScale = estimatedBodyHeight / 80; // Normalize to expected body height ratio

    // Primary detected joints with confidence weighting
    const rawHip = { x: rawPose.hip_x || 50, y: rawPose.hip_y || 50 };
    const rawKnee = { x: rawPose.knee_x || 50, y: rawPose.knee_y || 70 };
    const rawAnkle = { x: rawPose.ankle_x || 50, y: rawPose.ankle_y || 90 };
    const rawTrunk = { x: rawPose.trunk_x || 50, y: rawPose.trunk_y || 30 };
    const rawNeck = { x: rawPose.neck_x || 50, y: rawPose.neck_y || 15 };

    // ANATOMICALLY ACCURATE SPINE CONSTRUCTION
    const spineBase = { x: rawTrunk.x, y: rawHip.y - (ANATOMICAL_RATIOS.spineL5ToSacrum * 100) };

    const skeleton: ProfessionalSkeleton = {
      // CENTRAL AXIS - anatomically accurate spine
      head: {
        x: rawNeck.x,
        y: rawNeck.y - (ANATOMICAL_RATIOS.headToNeck * 100 * bodyScale),
        confidence: baseConfidence * 0.9,
        anatomicallyAdjusted: true,
        detected: false
      },
      neck: {
        x: rawNeck.x,
        y: rawNeck.y,
        confidence: baseConfidence,
        anatomicallyAdjusted: false,
        detected: true
      },
      c7: {
        x: rawNeck.x,
        y: rawNeck.y + (ANATOMICAL_RATIOS.neckToShoulder * 100 * bodyScale),
        confidence: baseConfidence * 0.95,
        anatomicallyAdjusted: true,
        detected: false
      },
      t12: {
        x: rawTrunk.x,
        y: rawTrunk.y + (ANATOMICAL_RATIOS.spineC7ToT12 * 100 * bodyScale),
        confidence: baseConfidence * 0.9,
        anatomicallyAdjusted: true,
        detected: false
      },
      l5: {
        x: spineBase.x,
        y: spineBase.y,
        confidence: baseConfidence * 0.85,
        anatomicallyAdjusted: true,
        detected: false
      },
      sacrum: {
        x: spineBase.x,
        y: rawHip.y,
        confidence: baseConfidence * 0.8,
        anatomicallyAdjusted: true,
        detected: false
      },

      // HIP CENTER (anatomical center of pelvis)
      hipCenter: {
        x: rawTrunk.x,
        y: rawHip.y,
        confidence: baseConfidence,
        anatomicallyAdjusted: true,
        detected: false
      },

      // PRIMARY LEG - anatomically constrained
      hip: {
        x: rawHip.x,
        y: rawHip.y,
        confidence: baseConfidence,
        anatomicallyAdjusted: false,
        detected: true
      },
      knee: {
        x: rawKnee.x,
        y: rawKnee.y,
        confidence: baseConfidence,
        anatomicallyAdjusted: false,
        detected: true
      },
      ankle: {
        x: rawAnkle.x,
        y: rawAnkle.y,
        confidence: baseConfidence,
        anatomicallyAdjusted: false,
        detected: true
      },
      heel: {
        x: rawPose.heel_x !== undefined && rawPose.heel_x !== null ? rawPose.heel_x :
           (rawAnkle.x - (ANATOMICAL_RATIOS.ankleToHeel * 100 * bodyScale)),
        y: rawPose.heel_y !== undefined && rawPose.heel_y !== null ? rawPose.heel_y :
           (rawAnkle.y + (ANATOMICAL_RATIOS.ankleToHeel * 50 * bodyScale)),
        confidence: (rawPose.heel_x !== undefined && rawPose.heel_x !== null) ? baseConfidence * 0.9 : baseConfidence * 0.8,
        anatomicallyAdjusted: !(rawPose.heel_x !== undefined && rawPose.heel_x !== null),
        detected: !!(rawPose.heel_x !== undefined && rawPose.heel_x !== null)
      },
      forefoot: {
        x: rawPose.foot_x !== undefined && rawPose.foot_x !== null ? rawPose.foot_x :
           (rawAnkle.x + (ANATOMICAL_RATIOS.heelToForefoot * 100 * bodyScale)),
        y: rawPose.foot_y !== undefined && rawPose.foot_y !== null ? rawPose.foot_y :
           (rawAnkle.y + (ANATOMICAL_RATIOS.heelToForefoot * 30 * bodyScale)),
        confidence: (rawPose.foot_x !== undefined && rawPose.foot_x !== null) ? baseConfidence * 0.9 : baseConfidence * 0.7,
        anatomicallyAdjusted: !(rawPose.foot_x !== undefined && rawPose.foot_x !== null),
        detected: !!(rawPose.foot_x !== undefined && rawPose.foot_x !== null)
      },
      toes: {
        x: rawPose.foot_x !== undefined && rawPose.foot_x !== null ?
           rawPose.foot_x + (ANATOMICAL_RATIOS.forefootToToes * 80 * bodyScale) :
           (rawAnkle.x + (ANATOMICAL_RATIOS.forefootToToes * 120 * bodyScale)),
        y: rawPose.foot_y !== undefined && rawPose.foot_y !== null ?
           rawPose.foot_y + (ANATOMICAL_RATIOS.forefootToToes * 20 * bodyScale) :
           (rawAnkle.y + (ANATOMICAL_RATIOS.forefootToToes * 20 * bodyScale)),
        confidence: (rawPose.foot_x !== undefined && rawPose.foot_x !== null) ? baseConfidence * 0.8 : baseConfidence * 0.6,
        anatomicallyAdjusted: !(rawPose.foot_x !== undefined && rawPose.foot_x !== null),
        detected: !!(rawPose.foot_x !== undefined && rawPose.foot_x !== null)
      },

      // BILATERAL SYMMETRY with anatomical spacing
      hipMirrored: {
        x: rawTrunk.x - (rawHip.x - rawTrunk.x),
        y: rawHip.y,
        confidence: baseConfidence * 0.75,
        anatomicallyAdjusted: true,
        detected: false
      },
      kneeMirrored: {
        x: rawTrunk.x - (rawKnee.x - rawTrunk.x),
        y: rawKnee.y,
        confidence: baseConfidence * 0.75,
        anatomicallyAdjusted: true,
        detected: false
      },
      ankleMirrored: {
        x: rawTrunk.x - (rawAnkle.x - rawTrunk.x),
        y: rawAnkle.y,
        confidence: baseConfidence * 0.75,
        anatomicallyAdjusted: true,
        detected: false
      },
      heelMirrored: {
        x: rawTrunk.x - (rawAnkle.x - rawTrunk.x) - (ANATOMICAL_RATIOS.ankleToHeel * 100 * bodyScale),
        y: rawAnkle.y + (ANATOMICAL_RATIOS.ankleToHeel * 50 * bodyScale),
        confidence: baseConfidence * 0.6,
        anatomicallyAdjusted: true,
        detected: false
      },
      forefootMirrored: {
        x: rawTrunk.x - (rawAnkle.x - rawTrunk.x) + (ANATOMICAL_RATIOS.heelToForefoot * 100 * bodyScale),
        y: rawAnkle.y + (ANATOMICAL_RATIOS.heelToForefoot * 30 * bodyScale),
        confidence: baseConfidence * 0.5,
        anatomicallyAdjusted: true,
        detected: false
      },
      toesMirrored: {
        x: rawTrunk.x - (rawAnkle.x - rawTrunk.x) + (ANATOMICAL_RATIOS.forefootToToes * 120 * bodyScale),
        y: rawAnkle.y + (ANATOMICAL_RATIOS.forefootToToes * 20 * bodyScale),
        confidence: baseConfidence * 0.4,
        anatomicallyAdjusted: true,
        detected: false
      },

      // PRIMARY ARM - ALWAYS USE DETECTED KEYPOINTS WHEN AVAILABLE
      shoulder: {
        x: rawPose.shoulder_x !== undefined && rawPose.shoulder_x !== null ? rawPose.shoulder_x :
           (rawTrunk.x + (ANATOMICAL_RATIOS.shoulderWidth * 60 * bodyScale * (rawHip.x > rawTrunk.x ? 1 : -1))),
        y: rawPose.shoulder_y !== undefined && rawPose.shoulder_y !== null ? rawPose.shoulder_y :
           (rawNeck.y + (ANATOMICAL_RATIOS.neckToShoulder * 80 * bodyScale)),
        confidence: (rawPose.shoulder_x !== undefined && rawPose.shoulder_x !== null) ? baseConfidence : baseConfidence * 0.7,
        anatomicallyAdjusted: !(rawPose.shoulder_x !== undefined && rawPose.shoulder_x !== null),
        detected: !!(rawPose.shoulder_x !== undefined && rawPose.shoulder_x !== null)
      },
      elbow: {
        x: rawPose.elbow_x !== undefined && rawPose.elbow_x !== null ? rawPose.elbow_x :
           (rawTrunk.x + (ANATOMICAL_RATIOS.shoulderWidth * 80 * bodyScale * (rawHip.x > rawTrunk.x ? 1 : -1))),
        y: rawPose.elbow_y !== undefined && rawPose.elbow_y !== null ? rawPose.elbow_y :
           (rawNeck.y + (ANATOMICAL_RATIOS.shoulderToElbow * 120 * bodyScale)),
        confidence: (rawPose.elbow_x !== undefined && rawPose.elbow_x !== null) ? baseConfidence : baseConfidence * 0.6,
        anatomicallyAdjusted: !(rawPose.elbow_x !== undefined && rawPose.elbow_x !== null),
        detected: !!(rawPose.elbow_x !== undefined && rawPose.elbow_x !== null)
      },
      wrist: {
        x: rawPose.wrist_x !== undefined && rawPose.wrist_x !== null ? rawPose.wrist_x :
           (rawTrunk.x + (ANATOMICAL_RATIOS.shoulderWidth * 90 * bodyScale * (rawHip.x > rawTrunk.x ? 1 : -1))),
        y: rawPose.wrist_y !== undefined && rawPose.wrist_y !== null ? rawPose.wrist_y :
           (rawNeck.y + ((ANATOMICAL_RATIOS.shoulderToElbow + ANATOMICAL_RATIOS.elbowToWrist) * 120 * bodyScale)),
        confidence: (rawPose.wrist_x !== undefined && rawPose.wrist_x !== null) ? baseConfidence : baseConfidence * 0.5,
        anatomicallyAdjusted: !(rawPose.wrist_x !== undefined && rawPose.wrist_x !== null),
        detected: !!(rawPose.wrist_x !== undefined && rawPose.wrist_x !== null)
      },
      hand: {
        x: (rawPose.wrist_x !== undefined && rawPose.wrist_x !== null) ?
           rawPose.wrist_x + (ANATOMICAL_RATIOS.wristToHand * 80 * bodyScale * (rawPose.wrist_x > rawTrunk.x ? 1 : -1)) :
           (rawTrunk.x + (ANATOMICAL_RATIOS.shoulderWidth * 100 * bodyScale * (rawHip.x > rawTrunk.x ? 1 : -1))),
        y: (rawPose.wrist_y !== undefined && rawPose.wrist_y !== null) ?
           rawPose.wrist_y + (ANATOMICAL_RATIOS.wristToHand * 30 * bodyScale) :
           (rawNeck.y + ((ANATOMICAL_RATIOS.shoulderToElbow + ANATOMICAL_RATIOS.elbowToWrist + ANATOMICAL_RATIOS.wristToHand) * 120 * bodyScale)),
        confidence: (rawPose.wrist_x !== undefined && rawPose.wrist_x !== null) ? baseConfidence * 0.8 : baseConfidence * 0.4,
        anatomicallyAdjusted: !(rawPose.wrist_x !== undefined && rawPose.wrist_x !== null),
        detected: !!(rawPose.wrist_x !== undefined && rawPose.wrist_x !== null)
      },

      // SECONDARY ARM - mirrored with anatomical constraints
      shoulderMirrored: {
        x: rawTrunk.x - (ANATOMICAL_RATIOS.shoulderWidth * 60 * bodyScale * (rawHip.x > rawTrunk.x ? 1 : -1)),
        y: rawNeck.y + (ANATOMICAL_RATIOS.neckToShoulder * 80 * bodyScale),
        confidence: baseConfidence * 0.6,
        anatomicallyAdjusted: true,
        detected: false
      },
      elbowMirrored: {
        x: rawTrunk.x - (ANATOMICAL_RATIOS.shoulderWidth * 80 * bodyScale * (rawHip.x > rawTrunk.x ? 1 : -1)),
        y: rawNeck.y + (ANATOMICAL_RATIOS.shoulderToElbow * 120 * bodyScale),
        confidence: baseConfidence * 0.5,
        anatomicallyAdjusted: true,
        detected: false
      },
      wristMirrored: {
        x: rawTrunk.x - (ANATOMICAL_RATIOS.shoulderWidth * 90 * bodyScale * (rawHip.x > rawTrunk.x ? 1 : -1)),
        y: rawNeck.y + ((ANATOMICAL_RATIOS.shoulderToElbow + ANATOMICAL_RATIOS.elbowToWrist) * 120 * bodyScale),
        confidence: baseConfidence * 0.4,
        anatomicallyAdjusted: true,
        detected: false
      },
      handMirrored: {
        x: rawTrunk.x - (ANATOMICAL_RATIOS.shoulderWidth * 100 * bodyScale * (rawHip.x > rawTrunk.x ? 1 : -1)),
        y: rawNeck.y + ((ANATOMICAL_RATIOS.shoulderToElbow + ANATOMICAL_RATIOS.elbowToWrist + ANATOMICAL_RATIOS.wristToHand) * 120 * bodyScale),
        confidence: baseConfidence * 0.3,
        anatomicallyAdjusted: true,
        detected: false
      }
    };

    return skeleton;
  };

  // PRECISE INTERNAL ANGLE CALCULATION with biomechanical validation
  const calculatePreciseInternalAngle = (point1: BiomechanicalJoint, vertex: BiomechanicalJoint, point2: BiomechanicalJoint): number => {
    const vector1 = {
      x: point1.x - vertex.x,
      y: point1.y - vertex.y
    };

    const vector2 = {
      x: point2.x - vertex.x,
      y: point2.y - vertex.y
    };

    const dot = vector1.x * vector2.x + vector1.y * vector2.y;
    const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);
    const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);

    if (mag1 === 0 || mag2 === 0) return 180; // Default to straight angle

    const cosAngle = Math.max(-1, Math.min(1, dot / (mag1 * mag2)));
    const angleRad = Math.acos(cosAngle);
    return angleRad * (180 / Math.PI);
  };

  // COMPREHENSIVE ANGLE ANALYSIS
  const calculateAllBiomechanicalAngles = (skeleton: ProfessionalSkeleton): AnatomicalAngle[] => {
    return [
      {
        name: 'Hip Flexion',
        value: calculatePreciseInternalAngle(skeleton.t12, skeleton.hip, skeleton.knee),
        vertex: skeleton.hip,
        point1: skeleton.t12,
        point2: skeleton.knee,
        significance: 'primary',
        confidence: (skeleton.hip.confidence + skeleton.knee.confidence) / 2
      },
      {
        name: 'Knee Flexion',
        value: calculatePreciseInternalAngle(skeleton.hip, skeleton.knee, skeleton.ankle),
        vertex: skeleton.knee,
        point1: skeleton.hip,
        point2: skeleton.ankle,
        significance: 'primary',
        confidence: (skeleton.hip.confidence + skeleton.knee.confidence + skeleton.ankle.confidence) / 3
      },
      {
        name: 'Ankle Dorsiflexion',
        value: calculatePreciseInternalAngle(skeleton.knee, skeleton.ankle, skeleton.forefoot),
        vertex: skeleton.ankle,
        point1: skeleton.knee,
        point2: skeleton.forefoot,
        significance: 'primary',
        confidence: (skeleton.knee.confidence + skeleton.ankle.confidence + skeleton.forefoot.confidence) / 3
      },
      {
        name: 'Shoulder Flexion',
        value: calculatePreciseInternalAngle(skeleton.c7, skeleton.shoulder, skeleton.elbow),
        vertex: skeleton.shoulder,
        point1: skeleton.c7,
        point2: skeleton.elbow,
        significance: 'secondary',
        confidence: (skeleton.shoulder.confidence + skeleton.elbow.confidence) / 2
      },
      {
        name: 'Elbow Flexion',
        value: calculatePreciseInternalAngle(skeleton.shoulder, skeleton.elbow, skeleton.wrist),
        vertex: skeleton.elbow,
        point1: skeleton.shoulder,
        point2: skeleton.wrist,
        significance: 'secondary',
        confidence: (skeleton.shoulder.confidence + skeleton.elbow.confidence + skeleton.wrist.confidence) / 3
      },
      {
        name: 'Trunk Angle',
        value: 90 - Math.abs(Math.atan2(skeleton.hip.x - skeleton.c7.x, skeleton.hip.y - skeleton.c7.y) * 180 / Math.PI),
        vertex: skeleton.c7,
        point1: skeleton.head,
        point2: skeleton.hip,
        significance: 'secondary',
        confidence: (skeleton.c7.confidence + skeleton.hip.confidence) / 2
      }
    ];
  };

  // PROFESSIONAL ANGLE ARC GENERATION aligned with bone vectors
  const createProfessionalAngleArc = (angle: AnatomicalAngle, radius: number): string => {
    const { vertex, point1, point2, value } = angle;

    // Calculate unit vectors from vertex to both points
    const vec1 = {
      x: point1.x - vertex.x,
      y: point1.y - vertex.y
    };
    const vec2 = {
      x: point2.x - vertex.x,
      y: point2.y - vertex.y
    };

    // Normalize vectors
    const mag1 = Math.sqrt(vec1.x * vec1.x + vec1.y * vec1.y);
    const mag2 = Math.sqrt(vec2.x * vec2.x + vec2.y * vec2.y);

    if (mag1 === 0 || mag2 === 0) return '';

    vec1.x /= mag1; vec1.y /= mag1;
    vec2.x /= mag2; vec2.y /= mag2;

    // Calculate arc endpoints
    const arcStart = {
      x: vertex.x + vec1.x * radius,
      y: vertex.y + vec1.y * radius
    };

    const arcEnd = {
      x: vertex.x + vec2.x * radius,
      y: vertex.y + vec2.y * radius
    };

    // Determine sweep direction using cross product
    const cross = vec1.x * vec2.y - vec1.y * vec2.x;
    const sweepFlag = cross > 0 ? 1 : 0;
    const largeArcFlag = value > 180 ? 1 : 0;

    return `M ${vertex.x} ${vertex.y} L ${arcStart.x} ${arcStart.y} A ${radius} ${radius} 0 ${largeArcFlag} ${sweepFlag} ${arcEnd.x} ${arcEnd.y} Z`;
  };

  // ADAPTIVE SCALING SYSTEM
  const calculateAdaptiveScaling = (skeleton: ProfessionalSkeleton) => {
    const bodyHeight = Math.abs(skeleton.head.y - skeleton.ankle.y);
    const expectedBodyHeight = 75; // Expected percentage of video height
    const scale = Math.max(0.6, Math.min(1.8, expectedBodyHeight / bodyHeight));

    const videoElement = videoRef.current;
    const videoWidth = videoElement?.videoWidth || 1920;
    const videoHeight = videoElement?.videoHeight || 1080;
    const baseSize = Math.min(videoWidth, videoHeight);

    return {
      strokeWidth: Math.max(0.4, Math.round(baseSize / 500) * scale),
      primaryJointRadius: Math.max(1.2, Math.round(baseSize / 320) * scale),
      secondaryJointRadius: Math.max(0.8, Math.round(baseSize / 400) * scale),
      fontSize: Math.max(2.0, Math.round(baseSize / 280) * scale),
      arcRadius: Math.max(8, Math.round(baseSize / 140) * scale),
      labelWidth: Math.max(28, Math.round(baseSize / 60) * scale),
      labelHeight: Math.max(16, Math.round(baseSize / 80) * scale),
      scale: scale
    };
  };

  const renderProfessionalRunningAnalysis = () => {
    if (!currentPose || !currentPose.pose_detected) {
      return (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-black bg-opacity-75 text-white px-6 py-3 rounded-lg border border-gray-400">
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm font-medium">
                {isLoading ? 'Loading biomechanical data...' : 'No pose detected in current frame'}
              </span>
            </div>
          </div>
        </div>
      );
    }

    // Build professional skeleton with anatomical constraints
    const skeleton = applyAnatomicalConstraints(currentPose);
    const angles = calculateAllBiomechanicalAngles(skeleton);
    const scaling = calculateAdaptiveScaling(skeleton);

    return (
      <svg
        className="absolute inset-0 w-full h-full pointer-events-none"
        viewBox="0 0 100 100"
        preserveAspectRatio="xMidYMid meet"
        style={{ zIndex: 50 }}
      >
        <defs>
          {/* Professional filters for medical-grade visualization */}
          <filter id="professionalGlow">
            <feGaussianBlur stdDeviation="0.3" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="professionalShadow">
            <feDropShadow dx="0.1" dy="0.1" stdDeviation="0.2" floodColor="black" floodOpacity="0.4"/>
          </filter>
          <filter id="labelShadow">
            <feDropShadow dx="0.2" dy="0.2" stdDeviation="0.4" floodColor="black" floodOpacity="0.6"/>
          </filter>

          {/* Gradient definitions for professional appearance */}
          <linearGradient id="primaryJointGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FF4444" stopOpacity="1"/>
            <stop offset="100%" stopColor="#CC0000" stopOpacity="1"/>
          </linearGradient>
          <linearGradient id="secondaryJointGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#4444FF" stopOpacity="0.8"/>
            <stop offset="100%" stopColor="#0000CC" stopOpacity="0.8"/>
          </linearGradient>
        </defs>

        {/* COMPREHENSIVE PROFESSIONAL SKELETON */}
        <g className="professional-skeleton">

          {/* CENTRAL SPINE - anatomically accurate vertebral column */}
          <g className="spinal-column">
            <line x1={skeleton.head.x} y1={skeleton.head.y} x2={skeleton.neck.x} y2={skeleton.neck.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.4} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
            <line x1={skeleton.neck.x} y1={skeleton.neck.y} x2={skeleton.c7.x} y2={skeleton.c7.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.6} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
            <line x1={skeleton.c7.x} y1={skeleton.c7.y} x2={skeleton.t12.x} y2={skeleton.t12.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.8} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
            <line x1={skeleton.t12.x} y1={skeleton.t12.y} x2={skeleton.l5.x} y2={skeleton.l5.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.6} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
            <line x1={skeleton.l5.x} y1={skeleton.l5.y} x2={skeleton.sacrum.x} y2={skeleton.sacrum.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.4} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
          </g>

          {/* PELVIC GIRDLE */}
          <g className="pelvic-girdle">
            <line x1={skeleton.sacrum.x} y1={skeleton.sacrum.y} x2={skeleton.hip.x} y2={skeleton.hip.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.5} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
            <line x1={skeleton.sacrum.x} y1={skeleton.sacrum.y} x2={skeleton.hipMirrored.x} y2={skeleton.hipMirrored.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.2} strokeLinecap="round"
                  opacity="0.75" filter="url(#professionalGlow)"/>
          </g>

          {/* PRIMARY LEG - detected side with high confidence */}
          <g className="primary-leg">
            <line x1={skeleton.hip.x} y1={skeleton.hip.y} x2={skeleton.knee.x} y2={skeleton.knee.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.8} strokeLinecap="round"
                  opacity="0.98" filter="url(#professionalGlow)"/>
            <line x1={skeleton.knee.x} y1={skeleton.knee.y} x2={skeleton.ankle.x} y2={skeleton.ankle.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.8} strokeLinecap="round"
                  opacity="0.98" filter="url(#professionalGlow)"/>

            {/* DETAILED FOOT STRUCTURE */}
            <line x1={skeleton.ankle.x} y1={skeleton.ankle.y} x2={skeleton.heel.x} y2={skeleton.heel.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.4} strokeLinecap="round"
                  opacity="0.90" filter="url(#professionalGlow)"/>
            <line x1={skeleton.heel.x} y1={skeleton.heel.y} x2={skeleton.forefoot.x} y2={skeleton.forefoot.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.3} strokeLinecap="round"
                  opacity="0.85" filter="url(#professionalGlow)"/>
            <line x1={skeleton.forefoot.x} y1={skeleton.forefoot.y} x2={skeleton.toes.x} y2={skeleton.toes.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.1} strokeLinecap="round"
                  opacity="0.80" filter="url(#professionalGlow)"/>
            <line x1={skeleton.ankle.x} y1={skeleton.ankle.y} x2={skeleton.forefoot.x} y2={skeleton.forefoot.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.0} strokeLinecap="round"
                  opacity="0.75" filter="url(#professionalGlow)"/>
          </g>

          {/* SECONDARY LEG - mirrored side with reduced opacity */}
          <g className="secondary-leg">
            <line x1={skeleton.hipMirrored.x} y1={skeleton.hipMirrored.y} x2={skeleton.kneeMirrored.x} y2={skeleton.kneeMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.2} strokeLinecap="round"
                  opacity="0.65" filter="url(#professionalGlow)"/>
            <line x1={skeleton.kneeMirrored.x} y1={skeleton.kneeMirrored.y} x2={skeleton.ankleMirrored.x} y2={skeleton.ankleMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.2} strokeLinecap="round"
                  opacity="0.65" filter="url(#professionalGlow)"/>

            {/* Mirrored foot structure */}
            <line x1={skeleton.ankleMirrored.x} y1={skeleton.ankleMirrored.y} x2={skeleton.heelMirrored.x} y2={skeleton.heelMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.0} strokeLinecap="round"
                  opacity="0.55" filter="url(#professionalGlow)"/>
            <line x1={skeleton.heelMirrored.x} y1={skeleton.heelMirrored.y} x2={skeleton.forefootMirrored.x} y2={skeleton.forefootMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 0.9} strokeLinecap="round"
                  opacity="0.50" filter="url(#professionalGlow)"/>
            <line x1={skeleton.forefootMirrored.x} y1={skeleton.forefootMirrored.y} x2={skeleton.toesMirrored.x} y2={skeleton.toesMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 0.8} strokeLinecap="round"
                  opacity="0.45" filter="url(#professionalGlow)"/>
            <line x1={skeleton.ankleMirrored.x} y1={skeleton.ankleMirrored.y} x2={skeleton.forefootMirrored.x} y2={skeleton.forefootMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 0.7} strokeLinecap="round"
                  opacity="0.40" filter="url(#professionalGlow)"/>
          </g>

          {/* SHOULDER GIRDLE AND ARMS */}
          <g className="upper-extremities">
            {/* Shoulder connections to spine */}
            <line x1={skeleton.c7.x} y1={skeleton.c7.y} x2={skeleton.shoulder.x} y2={skeleton.shoulder.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.3} strokeLinecap="round"
                  opacity={skeleton.shoulder.confidence} filter="url(#professionalGlow)"/>
            <line x1={skeleton.c7.x} y1={skeleton.c7.y} x2={skeleton.shoulderMirrored.x} y2={skeleton.shoulderMirrored.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.0} strokeLinecap="round"
                  opacity={skeleton.shoulderMirrored.confidence} filter="url(#professionalGlow)"/>

            {/* Primary arm */}
            <line x1={skeleton.shoulder.x} y1={skeleton.shoulder.y} x2={skeleton.elbow.x} y2={skeleton.elbow.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 1.4} strokeLinecap="round"
                  opacity={skeleton.elbow.confidence} filter="url(#professionalGlow)"/>
            <line x1={skeleton.elbow.x} y1={skeleton.elbow.y} x2={skeleton.wrist.x} y2={skeleton.wrist.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 1.2} strokeLinecap="round"
                  opacity={skeleton.wrist.confidence} filter="url(#professionalGlow)"/>
            <line x1={skeleton.wrist.x} y1={skeleton.wrist.y} x2={skeleton.hand.x} y2={skeleton.hand.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 1.0} strokeLinecap="round"
                  opacity={skeleton.hand.confidence} filter="url(#professionalGlow)"/>

            {/* Secondary arm */}
            <line x1={skeleton.shoulderMirrored.x} y1={skeleton.shoulderMirrored.y} x2={skeleton.elbowMirrored.x} y2={skeleton.elbowMirrored.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 1.0} strokeLinecap="round"
                  opacity={skeleton.elbowMirrored.confidence} filter="url(#professionalGlow)"/>
            <line x1={skeleton.elbowMirrored.x} y1={skeleton.elbowMirrored.y} x2={skeleton.wristMirrored.x} y2={skeleton.wristMirrored.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 0.9} strokeLinecap="round"
                  opacity={skeleton.wristMirrored.confidence} filter="url(#professionalGlow)"/>
            <line x1={skeleton.wristMirrored.x} y1={skeleton.wristMirrored.y} x2={skeleton.handMirrored.x} y2={skeleton.handMirrored.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 0.8} strokeLinecap="round"
                  opacity={skeleton.handMirrored.confidence} filter="url(#professionalGlow)"/>
          </g>
        </g>

        {/* PROFESSIONAL ANGLE ARCS - precisely aligned with bone segments */}
        <g className="biomechanical-angle-arcs">
          {angles.filter(angle => angle.significance === 'primary' && angle.confidence > 0.7).map((angle, index) => (
            <path key={`primary-arc-${index}`}
                  d={createProfessionalAngleArc(angle, scaling.arcRadius * (angle.significance === 'primary' ? 1.0 : 0.8))}
                  fill="rgba(255,165,0,0.5)"
                  stroke="rgba(255,140,0,0.95)"
                  strokeWidth="0.8"
                  opacity={angle.confidence}
                  filter="url(#professionalShadow)"/>
          ))}
          {angles.filter(angle => angle.significance === 'secondary' && angle.confidence > 0.6).map((angle, index) => (
            <path key={`secondary-arc-${index}`}
                  d={createProfessionalAngleArc(angle, scaling.arcRadius * 0.7)}
                  fill="rgba(255,165,0,0.3)"
                  stroke="rgba(255,140,0,0.8)"
                  strokeWidth="0.6"
                  opacity={angle.confidence * 0.8}
                  filter="url(#professionalShadow)"/>
          ))}
        </g>

        {/* PROFESSIONAL JOINT MARKERS - anatomically sized and positioned */}
        <g className="anatomical-joint-markers">
          {/* SPINE JOINTS */}
          <circle cx={skeleton.head.x} cy={skeleton.head.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="#87CEEB" stroke="white" strokeWidth={scaling.strokeWidth * 0.3}
                  opacity="0.9" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.neck.x} cy={skeleton.neck.y} r={scaling.secondaryJointRadius}
                  fill="#0066CC" stroke="white" strokeWidth={scaling.strokeWidth * 0.3}
                  opacity="0.95" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.c7.x} cy={skeleton.c7.y} r={scaling.secondaryJointRadius * 0.9}
                  fill="#0066CC" stroke="white" strokeWidth={scaling.strokeWidth * 0.25}
                  opacity="0.9" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.t12.x} cy={skeleton.t12.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="#0066CC" stroke="white" strokeWidth={scaling.strokeWidth * 0.2}
                  opacity="0.85" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.l5.x} cy={skeleton.l5.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="#0066CC" stroke="white" strokeWidth={scaling.strokeWidth * 0.2}
                  opacity="0.85" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.sacrum.x} cy={skeleton.sacrum.y} r={scaling.secondaryJointRadius * 0.9}
                  fill="#0066CC" stroke="white" strokeWidth={scaling.strokeWidth * 0.25}
                  opacity="0.9" filter="url(#professionalShadow)"/>

          {/* PRIMARY LEG JOINTS */}
          <circle cx={skeleton.hip.x} cy={skeleton.hip.y} r={scaling.primaryJointRadius}
                  fill="url(#primaryJointGradient)" stroke="white" strokeWidth={scaling.strokeWidth * 0.4}
                  opacity="0.98" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.knee.x} cy={skeleton.knee.y} r={scaling.primaryJointRadius * 1.1}
                  fill="#FFDD00" stroke="white" strokeWidth={scaling.strokeWidth * 0.4}
                  opacity="0.98" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.ankle.x} cy={skeleton.ankle.y} r={scaling.primaryJointRadius}
                  fill="#00FF44" stroke="white" strokeWidth={scaling.strokeWidth * 0.4}
                  opacity="0.95" filter="url(#professionalShadow)"/>

          {/* FOOT DETAIL JOINTS */}
          <circle cx={skeleton.heel.x} cy={skeleton.heel.y} r={scaling.secondaryJointRadius * 0.7}
                  fill="#00CC33" stroke="white" strokeWidth={scaling.strokeWidth * 0.2}
                  opacity={skeleton.heel.confidence} filter="url(#professionalShadow)"/>
          <circle cx={skeleton.forefoot.x} cy={skeleton.forefoot.y} r={scaling.secondaryJointRadius * 0.6}
                  fill="#00AA22" stroke="white" strokeWidth={scaling.strokeWidth * 0.15}
                  opacity={skeleton.forefoot.confidence} filter="url(#professionalShadow)"/>
          <circle cx={skeleton.toes.x} cy={skeleton.toes.y} r={scaling.secondaryJointRadius * 0.5}
                  fill="#008811" stroke="white" strokeWidth={scaling.strokeWidth * 0.1}
                  opacity={skeleton.toes.confidence} filter="url(#professionalShadow)"/>

          {/* SECONDARY LEG JOINTS */}
          <circle cx={skeleton.hipMirrored.x} cy={skeleton.hipMirrored.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="url(#secondaryJointGradient)" stroke="white" strokeWidth={scaling.strokeWidth * 0.25}
                  opacity="0.7" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.kneeMirrored.x} cy={skeleton.kneeMirrored.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="#CCAA00" stroke="white" strokeWidth={scaling.strokeWidth * 0.25}
                  opacity="0.7" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.ankleMirrored.x} cy={skeleton.ankleMirrored.y} r={scaling.secondaryJointRadius * 0.7}
                  fill="#00CC22" stroke="white" strokeWidth={scaling.strokeWidth * 0.2}
                  opacity="0.65" filter="url(#professionalShadow)"/>

          {/* ARM JOINTS */}
          <circle cx={skeleton.shoulder.x} cy={skeleton.shoulder.y} r={scaling.secondaryJointRadius * 0.9}
                  fill="#FF8800" stroke="white" strokeWidth={scaling.strokeWidth * 0.25}
                  opacity={skeleton.shoulder.confidence} filter="url(#professionalShadow)"/>
          <circle cx={skeleton.elbow.x} cy={skeleton.elbow.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="#FFAA00" stroke="white" strokeWidth={scaling.strokeWidth * 0.2}
                  opacity={skeleton.elbow.confidence} filter="url(#professionalShadow)"/>
          <circle cx={skeleton.wrist.x} cy={skeleton.wrist.y} r={scaling.secondaryJointRadius * 0.7}
                  fill="#FFCC00" stroke="white" strokeWidth={scaling.strokeWidth * 0.15}
                  opacity={skeleton.wrist.confidence} filter="url(#professionalShadow)"/>
          <circle cx={skeleton.hand.x} cy={skeleton.hand.y} r={scaling.secondaryJointRadius * 0.6}
                  fill="#FFDD44" stroke="white" strokeWidth={scaling.strokeWidth * 0.1}
                  opacity={skeleton.hand.confidence} filter="url(#professionalShadow)"/>
        </g>

        {/* CONSISTENT PROFESSIONAL ANGLE LABELS */}
        <g className="professional-angle-labels">
          {angles.filter(angle => angle.confidence > 0.7).map((angle, index) => {
            const labelX = angle.vertex.x - (scaling.labelWidth / 2);
            const labelY = angle.vertex.y - scaling.labelHeight - (scaling.arcRadius * 1.5) - (index * 2);

            return (
              <g key={`angle-label-${index}`}>
                <rect x={labelX} y={labelY}
                      width={scaling.labelWidth} height={scaling.labelHeight}
                      rx="3" ry="3"
                      fill="rgba(255,140,0,0.95)"
                      stroke="white"
                      strokeWidth="0.4"
                      filter="url(#labelShadow)"/>
                <text x={angle.vertex.x} y={labelY + (scaling.labelHeight * 0.7)}
                      textAnchor="middle"
                      fill="white"
                      fontSize={scaling.fontSize}
                      fontWeight="bold"
                      fontFamily="Arial, sans-serif">
                  {Math.round(angle.value)}°
                </text>
              </g>
            );
          })}
        </g>
      </svg>
    );
  };

  return (
    <div className="absolute inset-0 pointer-events-none" style={{ zIndex: 20 }}>
      {error && (
        <div className="absolute top-2 left-2 bg-red-500 text-white px-3 py-2 rounded text-sm z-30">
          {error}
        </div>
      )}

      {activity === 'running' && view === 'side' && renderProfessionalRunningAnalysis()}
    </div>
  );
};

export default SkeletalOverlay;