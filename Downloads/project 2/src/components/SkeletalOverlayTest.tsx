import React, { useState, useRef } from 'react';
import SkeletalOverlay from './SkeletalOverlay';

// Mock pose data for testing
const mockPoseData = [
  {
    frame_number: 1,
    timestamp_seconds: 0.1,
    pose_detected: true,
    detection_confidence: 0.85,
    hip_angle: 165,
    knee_angle: 135,
    ankle_angle: 90,
    trunk_angle: 15,
    neck_angle: 10,
    hip_x: 45,
    hip_y: 55,
    knee_x: 47,
    knee_y: 75,
    ankle_x: 49,
    ankle_y: 90,
    trunk_x: 43,
    trunk_y: 35,
    neck_x: 42,
    neck_y: 15,
    shoulder_x: 43,
    shoulder_y: 35,
    elbow_x: 45,
    elbow_y: 50,
    wrist_x: 47,
    wrist_y: 65,
    heel_x: 48,
    heel_y: 92,
    foot_x: 50,
    foot_y: 93,
    shoulder_left_x: 43,
    shoulder_left_y: 35,
    shoulder_right_x: 41,
    shoulder_right_y: 35,
    hip_left_x: 45,
    hip_left_y: 55,
    hip_right_x: 41,
    hip_right_y: 55,
    knee_left_x: 47,
    knee_left_y: 75,
    knee_right_x: 39,
    knee_right_y: 75,
    ankle_left_x: 49,
    ankle_left_y: 90,
    ankle_right_x: 37,
    ankle_right_y: 90,
    detection_quality: 85,
    bilateral_symmetry: 92,
    stride_length: 1.2,
    foot_strike_type: 'midfoot',
    posture_score: 78
  }
];

const SkeletalOverlayTest: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(0.1);
  const [sessionId] = useState('test-session-123');
  const videoRef = useRef<HTMLVideoElement>(null);

  // Mock function to insert test data into Supabase
  const insertTestData = async () => {
    try {
      const { default: supabase } = await import('../utils/supabaseClient');
      
      console.log('Inserting test pose data...');
      
      // First, create a test session
      const { data: sessionData, error: sessionError } = await supabase
        .from('pose_sessions')
        .insert({
          id: sessionId,
          video_id: 'test-video-123',
          model_type: 'BlazePose',
          analysis_fps: 10
        })
        .select()
        .single();

      if (sessionError && !sessionError.message.includes('duplicate')) {
        console.error('Error creating test session:', sessionError);
        return;
      }

      // Then insert the test pose data
      const { error: poseError } = await supabase
        .from('pose_data')
        .insert(mockPoseData.map(data => ({
          ...data,
          session_id: sessionId
        })));

      if (poseError && !poseError.message.includes('duplicate')) {
        console.error('Error inserting test pose data:', poseError);
      } else {
        console.log('Test data inserted successfully!');
      }
    } catch (error) {
      console.error('Error in insertTestData:', error);
    }
  };

  const clearTestData = async () => {
    try {
      const { default: supabase } = await import('../utils/supabaseClient');
      
      console.log('Clearing test data...');
      
      // Delete pose data first (due to foreign key constraint)
      await supabase
        .from('pose_data')
        .delete()
        .eq('session_id', sessionId);

      // Then delete the session
      await supabase
        .from('pose_sessions')
        .delete()
        .eq('id', sessionId);

      console.log('Test data cleared!');
    } catch (error) {
      console.error('Error clearing test data:', error);
    }
  };

  return (
    <div className="p-4 bg-gray-100 min-h-screen">
      <h1 className="text-2xl font-bold mb-4">Skeletal Overlay Test</h1>
      
      <div className="mb-4 space-x-2">
        <button
          onClick={insertTestData}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Insert Test Data
        </button>
        <button
          onClick={clearTestData}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
        >
          Clear Test Data
        </button>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          Current Time: {currentTime.toFixed(2)}s
        </label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.01"
          value={currentTime}
          onChange={(e) => setCurrentTime(parseFloat(e.target.value))}
          className="w-full"
        />
      </div>

      <div className="relative bg-black rounded-lg" style={{ aspectRatio: '9/16', height: '600px' }}>
        {/* Mock video element */}
        <video
          ref={videoRef}
          className="w-full h-full object-contain opacity-30"
          style={{ background: 'linear-gradient(45deg, #333, #666)' }}
        />
        
        {/* Skeletal Overlay */}
        <SkeletalOverlay
          activity="running"
          view="side"
          currentTime={currentTime}
          duration={1}
          videoRef={videoRef}
          sessionId={sessionId}
        />
        
        {/* Test info overlay */}
        <div className="absolute top-4 right-4 bg-white bg-opacity-90 p-3 rounded text-sm">
          <div><strong>Test Session:</strong> {sessionId}</div>
          <div><strong>Activity:</strong> running</div>
          <div><strong>View:</strong> side</div>
          <div><strong>Time:</strong> {currentTime.toFixed(2)}s</div>
        </div>
      </div>

      <div className="mt-4 text-sm text-gray-600">
        <p><strong>Instructions:</strong></p>
        <ol className="list-decimal list-inside space-y-1">
          <li>Click "Insert Test Data" to add mock pose data to the database</li>
          <li>Use the time slider to see the skeletal overlay at different timestamps</li>
          <li>Check the browser console for debug logs</li>
          <li>Click "Clear Test Data" when done testing</li>
        </ol>
      </div>
    </div>
  );
};

export default SkeletalOverlayTest;
